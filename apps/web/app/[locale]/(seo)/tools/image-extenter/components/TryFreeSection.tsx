'use client'

import React from 'react'
import { useTranslations } from 'next-intl'
import { Link } from '@i18n/routing'

interface TryFreeSectionProps {
  toolUrl: string
}

const TryFreeSection: React.FC<TryFreeSectionProps> = ({ toolUrl }) => {
  const t = useTranslations('imageExtenter')

  return (
    <section className="py-20 bg-gradient-to-br from-slate-50 to-blue-50 relative overflow-hidden">
      {/* 背景装饰圆圈 */}
      <div className="absolute inset-0">
        {/* 左上角绿色圆圈 */}
        <div className="absolute top-10 left-10 w-16 h-16 bg-green-400 rounded-full opacity-60"></div>
        
        {/* 右上角绿色圆圈 */}
        <div className="absolute top-20 right-20 w-12 h-12 bg-green-500 rounded-full opacity-50"></div>
        
        {/* 左下角绿色圆圈 */}
        <div className="absolute bottom-32 left-16 w-10 h-10 bg-green-400 rounded-full opacity-70"></div>
        
        {/* 右下角绿色圆圈 */}
        <div className="absolute bottom-10 right-32 w-14 h-14 bg-green-500 rounded-full opacity-60"></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="max-w-6xl mx-auto">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {/* 左侧用户头像 */}
            <div className="relative">
              {/* 左上角头像 */}
              <div className="absolute top-0 left-8 w-20 h-20 rounded-full overflow-hidden border-4 border-white shadow-lg">
                <img
                  src="https://images.unsplash.com/photo-1494790108755-2616c9c0e8e3?w=100&h=100&fit=crop&crop=face"
                  alt="User avatar"
                  className="w-full h-full object-cover"
                />
              </div>
              
              {/* 左下角头像 */}
              <div className="absolute bottom-0 left-0 w-24 h-24 rounded-full overflow-hidden border-4 border-white shadow-lg">
                <img
                  src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face"
                  alt="User avatar"
                  className="w-full h-full object-cover"
                />
              </div>
              
              {/* 右上角头像 */}
              <div className="absolute top-8 right-0 w-28 h-28 rounded-full overflow-hidden border-4 border-white shadow-lg">
                <img
                  src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face"
                  alt="User avatar"
                  className="w-full h-full object-cover"
                />
              </div>
              
              {/* 右下角头像 */}
              <div className="absolute bottom-8 right-8 w-22 h-22 rounded-full overflow-hidden border-4 border-white shadow-lg">
                <img
                  src="https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&crop=face"
                  alt="User avatar"
                  className="w-full h-full object-cover"
                />
              </div>
              
              {/* 占位空间 */}
              <div className="h-80"></div>
            </div>

            {/* 右侧内容 */}
            <div className="text-center lg:text-left">
              {/* 中央图标 */}
              <div className="flex justify-center lg:justify-start mb-8">
                <div className="w-16 h-16 bg-gradient-to-br from-blue-600 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg">
                  <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                </div>
              </div>

              {/* 标题 */}
              <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6 leading-tight">
                Expand Images With AI<br />
                at Your Fingertips With<br />
                <span className="text-blue-600">YouCam App</span>
              </h2>

              {/* 应用商店按钮 */}
              <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                <Link
                  href="https://apps.apple.com"
                  className="inline-flex items-center justify-center px-6 py-3 bg-black text-white rounded-lg hover:bg-gray-800 transition-colors duration-200"
                >
                  <img
                    src="https://developer.apple.com/assets/elements/badges/download-on-the-app-store.svg"
                    alt="Download on the App Store"
                    className="h-10"
                  />
                </Link>
                
                <Link
                  href="https://play.google.com"
                  className="inline-flex items-center justify-center px-6 py-3 bg-black text-white rounded-lg hover:bg-gray-800 transition-colors duration-200"
                >
                  <img
                    src="https://play.google.com/intl/en_us/badges/static/images/badges/en_badge_web_generic.png"
                    alt="Get it on Google Play"
                    className="h-10"
                  />
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default TryFreeSection
