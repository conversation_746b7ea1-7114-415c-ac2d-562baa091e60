'use client'

import React from 'react'
import { Upload, Settings, Wand2, Download } from 'lucide-react'
import { useTranslations } from 'next-intl'

interface HowToGuideSectionProps {
  toolUrl: string
}

const HowToGuideSection: React.FC<HowToGuideSectionProps> = ({ toolUrl }) => {
  const t = useTranslations('imageExtenter')
  const steps = [
    {
      id: 1,
      icon: Upload,
      title: 'Upload Your Image',
      description: 'Upload the image you want to expand.',
      details: '(Supported formats: JPG, JPEG, PNG, TIFF, and GIF)',
      stepNumber: '1',
    },
    {
      id: 2,
      icon: Settings,
      title: 'Choose a Perfect Size',
      description: 'Tailor-Made for Social Stardom:',
      details:
        'Select the new size and ratio from over 20 size templates (including IG, FB, TikTok, etc.).',
      stepNumber: '2',
    },
    {
      id: 3,
      icon: Wand2,
      title: 'Expand Your Photos',
      description: 'Click the "Generate" button to Start.',
      details:
        'YouCam AI Image Extender will automatically expand backgrounds while maintaining context, creating seamless new spaces in your images without losing quality.',
      stepNumber: '3',
    },
    {
      id: 4,
      icon: Download,
      title: 'Download and Share',
      description:
        'Download your expanded image in the file format of your choice.',
      details:
        'Then, take to the social media stages and watch as the likes, loves, and wows roll in.',
      stepNumber: '4',
    },
  ]

  return (
    <section className="py-20 bg-slate-900 relative overflow-hidden">
      {/* 背景装饰 */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-10 w-32 h-32 bg-blue-500/10 rounded-full blur-2xl"></div>
        <div className="absolute bottom-20 right-10 w-40 h-40 bg-blue-500/10 rounded-full blur-2xl"></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="max-w-6xl mx-auto">
          {/* 标题部分 */}
          <div className="text-center mb-16">
            <div className="inline-flex items-center gap-2 bg-blue-500/10 rounded-full px-4 py-2 mb-6 border border-blue-500/20">
              <Wand2 className="w-4 h-4 text-blue-400" />
              <span className="text-blue-300 text-sm font-medium">
                How It Works
              </span>
            </div>

            <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-8">
              How to Expand Your Image with AI?
            </h2>
          </div>

          {/* 步骤展示 - 4步流程 */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
            {steps.map((step) => {
              const IconComponent = step.icon
              return (
                <div
                  key={step.id}
                  className="text-center group hover:transform hover:scale-105 transition-all duration-300"
                >
                  {/* 步骤编号和图标 */}
                  <div className="relative mb-6">
                    <div className="w-24 h-24 mx-auto bg-slate-800 rounded-2xl shadow-lg flex items-center justify-center border border-slate-700">
                      <IconComponent className="w-10 h-10 text-blue-400" />
                    </div>
                    <div className="absolute -top-2 -right-2 w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold">
                      {step.stepNumber}
                    </div>
                  </div>

                  {/* 步骤标题 */}
                  <h3 className="text-xl font-bold text-white mb-3">
                    {step.title}
                  </h3>

                  {/* 步骤描述 */}
                  <p className="text-gray-300 font-medium mb-2">
                    {step.description}
                  </p>

                  {/* 步骤详情 */}
                  <p className="text-gray-400 text-sm leading-relaxed">
                    {step.details}
                  </p>
                </div>
              )
            })}
          </div>

          {/* 流程总结 */}
          <div className="text-center bg-slate-800 rounded-2xl p-8 shadow-lg border border-slate-700">
            <div className="flex items-center justify-center gap-4 mb-6 flex-wrap">
              <div className="flex items-center gap-2">
                <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                  <span className="text-white text-sm font-bold">1</span>
                </div>
                <span className="text-gray-300 font-medium">Upload</span>
              </div>

              <div className="w-6 h-0.5 bg-blue-400 hidden sm:block"></div>

              <div className="flex items-center gap-2">
                <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                  <span className="text-white text-sm font-bold">2</span>
                </div>
                <span className="text-gray-300 font-medium">Choose Size</span>
              </div>

              <div className="w-6 h-0.5 bg-blue-400 hidden sm:block"></div>

              <div className="flex items-center gap-2">
                <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                  <span className="text-white text-sm font-bold">3</span>
                </div>
                <span className="text-gray-300 font-medium">Generate</span>
              </div>

              <div className="w-6 h-0.5 bg-blue-400 hidden sm:block"></div>

              <div className="flex items-center gap-2">
                <div className="w-8 h-8 bg-green-600 rounded-full flex items-center justify-center">
                  <Download className="w-4 h-4 text-white" />
                </div>
                <span className="text-green-400 font-semibold">Download</span>
              </div>
            </div>

            <p className="text-gray-300 text-lg">
              That's it! Your expanded image is ready in seconds.
            </p>
          </div>
        </div>
      </div>
    </section>
  )
}

export default HowToGuideSection
