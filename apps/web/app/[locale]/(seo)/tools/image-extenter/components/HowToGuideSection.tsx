'use client'

import React from 'react'
import { Upload, Wand2 } from 'lucide-react'
import { useTranslations } from 'next-intl'

interface HowToGuideSectionProps {
  toolUrl: string
}

const HowToGuideSection: React.FC<HowToGuideSectionProps> = ({ toolUrl }) => {
  const t = useTranslations('imageExtenter')
  const steps = [
    {
      id: 1,
      icon: Upload,
      title: t('newStep1Title'),
      description: t('newStep1Description'),
      stepNumber: '1',
    },
    {
      id: 2,
      icon: Wand2,
      title: t('newStep2Title'),
      description: t('newStep2Description'),
      stepNumber: '2',
    },
  ]

  return (
    <section className="py-20 bg-slate-900 relative overflow-hidden">
      {/* 背景装饰 */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-10 w-32 h-32 bg-blue-500/10 rounded-full blur-2xl"></div>
        <div className="absolute bottom-20 right-10 w-40 h-40 bg-blue-500/10 rounded-full blur-2xl"></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="max-w-6xl mx-auto">
          {/* 标题部分 */}
          <div className="text-center mb-16">
            <div className="inline-flex items-center gap-2 bg-blue-500/10 rounded-full px-4 py-2 mb-6 border border-blue-500/20">
              <Wand2 className="w-4 h-4 text-blue-400" />
              <span className="text-blue-300 text-sm font-medium">
                {t('howItWorksBadge')}
              </span>
            </div>

            <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-8">
              {t('newHowToTitle')}
            </h2>
            <p className="text-xl text-gray-300 max-w-4xl mx-auto leading-relaxed text-center">
              {t('newHowToDescription')}
            </p>
          </div>

          {/* 步骤展示 - 2步流程 */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-12 mb-16 max-w-4xl mx-auto">
            {steps.map((step) => {
              const IconComponent = step.icon
              return (
                <div
                  key={step.id}
                  className="text-center group hover:transform hover:scale-105 transition-all duration-300"
                >
                  {/* 步骤编号和图标 */}
                  <div className="relative mb-8">
                    <div className="w-32 h-32 mx-auto bg-gradient-to-br from-slate-800 to-slate-700 rounded-3xl shadow-xl flex items-center justify-center border border-slate-600">
                      <IconComponent className="w-14 h-14 text-blue-400" />
                    </div>
                    <div className="absolute -top-3 -right-3 w-12 h-12 bg-gradient-to-br from-blue-600 to-purple-600 text-white rounded-full flex items-center justify-center text-lg font-bold shadow-lg">
                      {step.stepNumber}
                    </div>
                  </div>

                  {/* 步骤标题 */}
                  <h3 className="text-2xl font-bold text-white mb-4">
                    {step.title}
                  </h3>

                  {/* 步骤描述 */}
                  <p className="text-gray-300 text-lg leading-relaxed">
                    {step.description}
                  </p>
                </div>
              )
            })}
          </div>

          {/* CTA按钮 */}
          <div className="text-center">
            <a
              href={toolUrl}
              className="inline-flex items-center gap-3 px-10 py-5 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-bold text-lg rounded-xl hover:from-blue-700 hover:to-purple-700 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105"
            >
              <Wand2 className="w-6 h-6" />
              <span>{t('howToCTAButton')}</span>
            </a>
          </div>
        </div>
      </div>
    </section>
  )
}

export default HowToGuideSection
