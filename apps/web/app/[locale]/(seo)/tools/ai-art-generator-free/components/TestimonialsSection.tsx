import { useTranslations } from 'next-intl'
import AnimatedCard from './AnimatedCard'

interface TestimonialsSectionProps {
  toolUrl: string
}

export default function TestimonialsSection({
  toolUrl,
}: TestimonialsSectionProps) {
  const t = useTranslations('aiArtGeneratorFree')
  const testimonials = [
    {
      content: t('testimonial1Content'),
      author: t('testimonial1Author'),
      role: t('testimonial1Role'),
      rating: 5,
    },
    {
      content: t('testimonial2Content'),
      author: t('testimonial2Author'),
      role: t('testimonial2Role'),
      rating: 5,
    },
    {
      content: t('testimonial3Content'),
      author: t('testimonial3Author'),
      role: t('testimonial3Role'),
      rating: 5,
    },
    {
      content: t('testimonial4Content'),
      author: t('testimonial4Author'),
      role: t('testimonial4Role'),
      rating: 5,
    },
  ]

  return (
    <section className="py-24 px-4 relative">
      <div className="max-w-6xl mx-auto">
        {/* Section Header - H2 with exact SEO content */}
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-5xl font-bold mb-6">
            <span className="text-pink-400">{t('testimonialsTitle')}</span>
          </h2>
          <div className="flex items-center justify-center gap-2 mb-6">
            <div className="flex text-yellow-400">
              {[...Array(5)].map((_, i) => (
                <i key={i} className="fas fa-star" />
              ))}
            </div>
            <span className="text-white/80 text-lg ml-2">
              {t('testimonialsRating')}
            </span>
          </div>
        </div>

        {/* Testimonials Grid */}
        <div className="grid md:grid-cols-2 gap-8">
          {testimonials.map((testimonial, index) => (
            <AnimatedCard key={index} delay={index * 100}>
              <div className="relative">
                {/* Quote Icon */}
                <div className="absolute -top-2 -left-2 text-purple-400/30 text-4xl">
                  <i className="fas fa-quote-left" />
                </div>

                {/* Rating Stars */}
                <div className="flex text-yellow-400 mb-4">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <i key={i} className="fas fa-star text-sm" />
                  ))}
                </div>

                {/* Testimonial Content */}
                <p className="text-white/90 text-lg leading-relaxed mb-6 relative z-10">
                  "{testimonial.content}"
                </p>

                {/* Author Info */}
                <div className="flex items-center gap-4">
                  <div className="w-12 h-12 bg-gradient-to-r from-purple-600 to-pink-600 rounded-full flex items-center justify-center">
                    <i className="fas fa-user text-white" />
                  </div>
                  <div>
                    <div className="font-semibold text-white">
                      {testimonial.author}
                    </div>
                    <div className="text-white/60 text-sm">
                      {testimonial.role}
                    </div>
                  </div>
                </div>
              </div>
            </AnimatedCard>
          ))}
        </div>

        {/* Stats Section */}
        <div className="mt-16 grid grid-cols-2 md:grid-cols-4 gap-8">
          <div className="text-center">
            <div className="text-3xl md:text-4xl font-bold text-white mb-2">
              {t('stat1Number')}
            </div>
            <div className="text-white/60">{t('stat1Label')}</div>
          </div>
          <div className="text-center">
            <div className="text-3xl md:text-4xl font-bold text-white mb-2">
              {t('stat2Number')}
            </div>
            <div className="text-white/60">{t('stat2Label')}</div>
          </div>
          <div className="text-center">
            <div className="text-3xl md:text-4xl font-bold text-white mb-2">
              {t('stat3Number')}
            </div>
            <div className="text-white/60">{t('stat3Label')}</div>
          </div>
          <div className="text-center">
            <div className="text-3xl md:text-4xl font-bold text-white mb-2">
              {t('stat4Number')}
            </div>
            <div className="text-white/60">{t('stat4Label')}</div>
          </div>
        </div>
      </div>
    </section>
  )
}
